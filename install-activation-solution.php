<?php
/**
 * WooCommerce PDF Invoices - Quick Installation Script
 * 
 * This script automates the installation and verification of the permanent activation solution.
 * Run this script once to set up the complete solution.
 * 
 * @version 1.0.0
 * <AUTHOR> Activation Solution
 */

// Prevent direct access unless in WordPress context
if (!defined('ABSPATH')) {
    // Allow direct execution for installation purposes
    if (php_sapi_name() === 'cli' || !empty($_GET['install_activation'])) {
        define('WP_USE_THEMES', false);
        
        // Try to find WordPress
        $wp_paths = array(
            '../../../wp-load.php',
            '../../../../wp-load.php',
            '../../../../../wp-load.php'
        );
        
        foreach ($wp_paths as $path) {
            if (file_exists($path)) {
                require_once $path;
                break;
            }
        }
        
        if (!defined('ABSPATH')) {
            die('WordPress not found. Please run this script from the plugin directory or access it via WordPress admin.');
        }
    } else {
        die('Direct access not allowed.');
    }
}

class WooCommerce_PDF_Invoices_Installer {
    
    private $plugin_dir;
    private $errors = array();
    private $success = array();
    
    public function __construct() {
        $this->plugin_dir = plugin_dir_path(__FILE__);
    }
    
    /**
     * Run the complete installation
     */
    public function install() {
        echo "<h1>WooCommerce PDF Invoices - Activation Solution Installer</h1>\n";
        
        // Check prerequisites
        if (!$this->check_prerequisites()) {
            $this->display_results();
            return false;
        }
        
        // Install activation solution
        $this->install_activation_solution();
        
        // Verify installation
        $this->verify_installation();
        
        // Display results
        $this->display_results();
        
        return empty($this->errors);
    }
    
    /**
     * Check prerequisites
     */
    private function check_prerequisites() {
        echo "<h2>Checking Prerequisites...</h2>\n";
        
        // Check if WordPress is loaded
        if (!function_exists('get_option')) {
            $this->errors[] = 'WordPress not properly loaded';
            return false;
        }
        
        // Check if WooCommerce is active
        if (!is_plugin_active('woocommerce/woocommerce.php')) {
            $this->errors[] = 'WooCommerce plugin is not active';
        } else {
            $this->success[] = 'WooCommerce is active';
        }
        
        // Check if main plugin file exists
        $main_plugin = $this->plugin_dir . 'woocommerce-ultimate-pdf-invoices.php';
        if (!file_exists($main_plugin)) {
            $this->errors[] = 'Main plugin file not found: ' . $main_plugin;
            return false;
        } else {
            $this->success[] = 'Main plugin file found';
        }
        
        // Check file permissions
        if (!is_writable($this->plugin_dir)) {
            $this->errors[] = 'Plugin directory is not writable: ' . $this->plugin_dir;
            return false;
        } else {
            $this->success[] = 'Plugin directory is writable';
        }
        
        // Check if includes directory exists
        $includes_dir = $this->plugin_dir . 'includes/';
        if (!is_dir($includes_dir)) {
            $this->errors[] = 'Includes directory not found: ' . $includes_dir;
            return false;
        } else {
            $this->success[] = 'Includes directory found';
        }
        
        return empty($this->errors);
    }
    
    /**
     * Install the activation solution
     */
    private function install_activation_solution() {
        echo "<h2>Installing Activation Solution...</h2>\n";
        
        // Check if activator file exists
        $activator_file = $this->plugin_dir . 'woocommerce-pdf-invoices-activator.php';
        if (file_exists($activator_file)) {
            $this->success[] = 'Activator file already exists';
        } else {
            $this->errors[] = 'Activator file not found. Please ensure all solution files are uploaded.';
            return;
        }
        
        // Check if updater file exists
        $updater_file = $this->plugin_dir . 'includes/class-woocommerce-pdf-invoices-updater.php';
        if (file_exists($updater_file)) {
            $this->success[] = 'Updater file already exists';
        } else {
            $this->errors[] = 'Updater file not found. Please ensure all solution files are uploaded.';
            return;
        }
        
        // Check if verification file exists
        $verification_file = $this->plugin_dir . 'woocommerce-pdf-invoices-verification.php';
        if (file_exists($verification_file)) {
            $this->success[] = 'Verification file already exists';
        } else {
            $this->errors[] = 'Verification file not found. Please ensure all solution files are uploaded.';
            return;
        }
        
        // Activate the license
        if (function_exists('woocommerce_pdf_invoices_fallback_activate')) {
            $result = woocommerce_pdf_invoices_fallback_activate();
            if ($result) {
                $this->success[] = 'License activated successfully';
            } else {
                $this->errors[] = 'License activation failed';
            }
        } else {
            // Try to include the activator and run it
            if (file_exists($activator_file)) {
                include_once $activator_file;
                if (function_exists('woocommerce_pdf_invoices_fallback_activate')) {
                    $result = woocommerce_pdf_invoices_fallback_activate();
                    if ($result) {
                        $this->success[] = 'License activated successfully (manual include)';
                    } else {
                        $this->errors[] = 'License activation failed (manual include)';
                    }
                } else {
                    $this->errors[] = 'Activation function not available after include';
                }
            }
        }
    }
    
    /**
     * Verify the installation
     */
    private function verify_installation() {
        echo "<h2>Verifying Installation...</h2>\n";
        
        // Check activation status
        if (function_exists('woocommerce_pdf_invoices_verify_status')) {
            $status = woocommerce_pdf_invoices_verify_status();
            
            if ($status['activated']) {
                $this->success[] = 'Plugin is activated';
            } else {
                $this->errors[] = 'Plugin activation verification failed';
            }
            
            if ($status['license_status'] === 'valid') {
                $this->success[] = 'License status is valid';
            } else {
                $this->errors[] = 'License status is invalid';
            }
            
            if ($status['update_capability']) {
                $this->success[] = 'Update capability is available';
            } else {
                $this->errors[] = 'Update capability is not available';
            }
        } else {
            $this->errors[] = 'Verification function not available';
        }
        
        // Check global license variable
        global $weLaunchLicenses;
        if (isset($weLaunchLicenses['woocommerce-ultimate-pdf-invoices']) || 
            isset($weLaunchLicenses['woocommerce-plugin-bundle'])) {
            $this->success[] = 'Global license variable is set';
        } else {
            $this->errors[] = 'Global license variable is not set';
        }
        
        // Check WordPress options
        if (get_option('woocommerce_pdf_invoices_permanently_activated')) {
            $this->success[] = 'Permanent activation flag is set';
        } else {
            $this->errors[] = 'Permanent activation flag is not set';
        }
    }
    
    /**
     * Display installation results
     */
    private function display_results() {
        echo "<h2>Installation Results</h2>\n";
        
        if (!empty($this->success)) {
            echo "<h3 style='color: green;'>✓ Success:</h3>\n";
            echo "<ul>\n";
            foreach ($this->success as $message) {
                echo "<li style='color: green;'>✓ " . htmlspecialchars($message) . "</li>\n";
            }
            echo "</ul>\n";
        }
        
        if (!empty($this->errors)) {
            echo "<h3 style='color: red;'>✗ Errors:</h3>\n";
            echo "<ul>\n";
            foreach ($this->errors as $error) {
                echo "<li style='color: red;'>✗ " . htmlspecialchars($error) . "</li>\n";
            }
            echo "</ul>\n";
        }
        
        if (empty($this->errors)) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 20px 0; border-radius: 4px;'>\n";
            echo "<h3>🎉 Installation Completed Successfully!</h3>\n";
            echo "<p>The WooCommerce PDF Invoices plugin is now permanently activated with update functionality preserved.</p>\n";
            echo "<p><strong>Next Steps:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Go to <strong>WooCommerce → PDF Invoices</strong> to configure the plugin</li>\n";
            echo "<li>Visit <strong>WooCommerce → PDF Invoices Status</strong> to verify the solution</li>\n";
            echo "<li>Test PDF generation with a sample order</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 4px;'>\n";
            echo "<h3>⚠️ Installation Issues Detected</h3>\n";
            echo "<p>Please resolve the errors above and try again. You can also try the manual activation methods described in the documentation.</p>\n";
            echo "</div>\n";
        }
        
        echo "<hr>\n";
        echo "<p><strong>Documentation:</strong> See ACTIVATION_SOLUTION_README.md for detailed instructions and troubleshooting.</p>\n";
        echo "<p><strong>Support:</strong> Check the verification page at WooCommerce → PDF Invoices Status for additional tools.</p>\n";
    }
}

// Run the installer
if (defined('ABSPATH')) {
    $installer = new WooCommerce_PDF_Invoices_Installer();
    $installer->install();
} else {
    echo "Please run this script through WordPress or ensure WordPress is properly loaded.\n";
}
?>
