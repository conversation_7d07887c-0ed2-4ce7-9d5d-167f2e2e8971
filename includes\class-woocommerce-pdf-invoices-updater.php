<?php
/**
 * WooCommerce PDF Invoices - Enhanced Update Checker
 * 
 * This class handles plugin updates while maintaining communication with the original server
 * and preserving the permanent activation status.
 * 
 * @version 1.0.0
 * <AUTHOR> Activation Solution
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCommerce_PDF_Invoices_Updater {
    
    private $plugin_file;
    private $plugin_slug;
    private $version;
    private $cache_key;
    private $cache_allowed;
    
    public function __construct($plugin_file, $version) {
        $this->plugin_file = $plugin_file;
        $this->plugin_slug = plugin_basename($plugin_file);
        $this->version = $version;
        $this->cache_key = 'woocommerce_pdf_invoices_update_check';
        $this->cache_allowed = true;
        
        add_filter('pre_set_site_transient_update_plugins', array($this, 'modify_transient'), 10, 1);
        add_filter('plugins_api', array($this, 'plugin_popup'), 10, 3);
        add_filter('upgrader_post_install', array($this, 'after_install'), 10, 3);
        
        // Clear cache when needed
        add_action('upgrader_process_complete', array($this, 'purge_cache'));
    }
    
    /**
     * Modify the plugin update transient
     */
    public function modify_transient($transient) {
        if (empty($transient->checked)) {
            return $transient;
        }
        
        // Get remote version info
        $remote_version = $this->request();
        
        if ($remote_version && version_compare($this->version, $remote_version->new_version, '<')) {
            $transient->response[$this->plugin_slug] = $remote_version;
        }
        
        return $transient;
    }
    
    /**
     * Request version information from remote server
     */
    public function request() {
        $remote_get = $this->remote_get();
        
        if (!$remote_get || is_wp_error($remote_get) || wp_remote_retrieve_response_code($remote_get) != 200) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($remote_get);
        $data = json_decode($body, true);
        
        if (!$data) {
            return false;
        }
        
        return (object) array(
            'slug' => $this->plugin_slug,
            'new_version' => $data['new_version'],
            'url' => $data['details_url'],
            'package' => $data['download_url'],
            'tested' => $data['tested'],
            'requires_php' => $data['requires_php'],
            'compatibility' => $data['compatibility']
        );
    }
    
    /**
     * Make remote GET request
     */
    private function remote_get() {
        $request = wp_remote_get(
            $this->get_api_url(),
            array(
                'timeout' => 10,
                'headers' => array(
                    'Accept' => 'application/json',
                    'User-Agent' => $this->get_user_agent()
                )
            )
        );
        
        return $request;
    }
    
    /**
     * Get API URL for version checking
     */
    private function get_api_url() {
        $api_url = 'https://api.welaunch.io/v1/check-version';
        
        $query_args = array(
            'slug' => 'woocommerce-ultimate-pdf-invoices',
            'version' => $this->version,
            'site_url' => get_site_url(),
            'php_version' => phpversion(),
            'wp_version' => get_bloginfo('version')
        );
        
        return add_query_arg($query_args, $api_url);
    }
    
    /**
     * Get user agent string
     */
    private function get_user_agent() {
        return 'WordPress/' . get_bloginfo('version') . '; ' . get_bloginfo('url') . '; WooCommerce-PDF-Invoices/' . $this->version;
    }
    
    /**
     * Handle plugin information popup
     */
    public function plugin_popup($result, $action, $args) {
        if ($action !== 'plugin_information') {
            return $result;
        }
        
        if ($args->slug !== 'woocommerce-ultimate-pdf-invoices') {
            return $result;
        }
        
        $remote_get = $this->remote_get();
        
        if (!$remote_get || is_wp_error($remote_get) || wp_remote_retrieve_response_code($remote_get) != 200) {
            return $result;
        }
        
        $body = wp_remote_retrieve_body($remote_get);
        $data = json_decode($body, true);
        
        if (!$data) {
            return $result;
        }
        
        return (object) array(
            'name' => $data['name'],
            'slug' => $data['slug'],
            'version' => $data['new_version'],
            'author' => $data['author'],
            'author_profile' => $data['author_profile'],
            'requires' => $data['requires'],
            'tested' => $data['tested'],
            'requires_php' => $data['requires_php'],
            'download_link' => $data['download_url'],
            'trunk' => $data['download_url'],
            'last_updated' => $data['last_updated'],
            'sections' => array(
                'description' => $data['sections']['description'],
                'installation' => $data['sections']['installation'],
                'changelog' => $data['sections']['changelog']
            ),
            'banners' => $data['banners'],
            'icons' => $data['icons']
        );
    }
    
    /**
     * Handle post-installation tasks
     */
    public function after_install($response, $hook_extra, $result) {
        global $wp_filesystem;
        
        $install_directory = plugin_dir_path($this->plugin_file);
        $wp_filesystem->move($result['destination'], $install_directory);
        $result['destination'] = $install_directory;
        
        // Ensure permanent activation persists after update
        if (function_exists('woocommerce_pdf_invoices_fallback_activate')) {
            woocommerce_pdf_invoices_fallback_activate();
        }
        
        // Re-activate the plugin if it was active
        if (is_plugin_active($this->plugin_slug)) {
            activate_plugin($this->plugin_slug);
        }
        
        return $result;
    }
    
    /**
     * Purge update cache
     */
    public function purge_cache() {
        delete_transient($this->cache_key);
    }
    
    /**
     * Check if updates are available
     */
    public function has_update() {
        $remote_version = $this->request();
        
        if ($remote_version && version_compare($this->version, $remote_version->new_version, '<')) {
            return $remote_version;
        }
        
        return false;
    }
    
    /**
     * Get current plugin information
     */
    public function get_plugin_info() {
        return array(
            'slug' => $this->plugin_slug,
            'version' => $this->version,
            'file' => $this->plugin_file,
            'cache_key' => $this->cache_key,
            'update_available' => $this->has_update() !== false
        );
    }
    
    /**
     * Force update check
     */
    public function force_update_check() {
        $this->purge_cache();
        return $this->request();
    }
}

/**
 * Initialize the updater
 */
function woocommerce_pdf_invoices_init_updater() {
    if (is_admin()) {
        $plugin_file = plugin_dir_path(dirname(__FILE__)) . 'woocommerce-ultimate-pdf-invoices.php';
        $plugin_data = get_plugin_data($plugin_file);
        $version = $plugin_data['Version'];
        
        new WooCommerce_PDF_Invoices_Updater($plugin_file, $version);
    }
}

add_action('init', 'woocommerce_pdf_invoices_init_updater');
