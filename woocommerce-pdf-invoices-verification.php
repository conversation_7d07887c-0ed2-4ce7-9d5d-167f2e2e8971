<?php
/**
 * WooCommerce PDF Invoices - Verification and Management Tools
 * 
 * This file provides tools to verify activation status, manage the solution,
 * and troubleshoot any issues.
 * 
 * @version 1.0.0
 * <AUTHOR> Activation Solution
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCommerce_PDF_Invoices_Verification {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_wc_pdf_invoices_verify', array($this, 'ajax_verify_status'));
        add_action('wp_ajax_wc_pdf_invoices_reactivate', array($this, 'ajax_reactivate'));
        add_action('wp_ajax_wc_pdf_invoices_check_updates', array($this, 'ajax_check_updates'));
    }
    
    /**
     * Add admin menu page
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            'PDF Invoices Status',
            'PDF Invoices Status',
            'manage_options',
            'wc-pdf-invoices-status',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>WooCommerce PDF Invoices - Activation Status</h1>
            
            <div id="wc-pdf-status-container">
                <div class="card">
                    <h2>Current Status</h2>
                    <div id="status-display">
                        <p>Loading status...</p>
                    </div>
                    <button type="button" class="button button-primary" id="verify-status">Refresh Status</button>
                    <button type="button" class="button button-secondary" id="reactivate-plugin">Re-activate</button>
                </div>
                
                <div class="card">
                    <h2>Update Information</h2>
                    <div id="update-display">
                        <p>Checking for updates...</p>
                    </div>
                    <button type="button" class="button button-secondary" id="check-updates">Check for Updates</button>
                </div>
                
                <div class="card">
                    <h2>Manual Activation Methods</h2>
                    <p>If automatic activation fails, you can use these manual methods:</p>
                    
                    <h3>Method 1: PHP Code</h3>
                    <p>Add this code to your theme's functions.php file:</p>
                    <code style="display: block; background: #f1f1f1; padding: 10px; margin: 10px 0;">
// WooCommerce PDF Invoices Permanent Activation<br>
add_action('init', function() {<br>
&nbsp;&nbsp;&nbsp;&nbsp;global $weLaunchLicenses;<br>
&nbsp;&nbsp;&nbsp;&nbsp;$weLaunchLicenses['woocommerce-ultimate-pdf-invoices'] = array('status' => 'valid');<br>
&nbsp;&nbsp;&nbsp;&nbsp;$weLaunchLicenses['woocommerce-plugin-bundle'] = array('status' => 'valid');<br>
}, 1);
                    </code>
                    
                    <h3>Method 2: WordPress Admin</h3>
                    <p>Go to Tools → Site Health → Info → Constants and verify that the plugin constants are set correctly.</p>
                    
                    <h3>Method 3: Database Direct</h3>
                    <p>Execute this SQL query in your database:</p>
                    <code style="display: block; background: #f1f1f1; padding: 10px; margin: 10px 0;">
INSERT INTO wp_options (option_name, option_value, autoload) VALUES<br>
('woocommerce_pdf_invoices_permanently_activated', '1', 'yes')<br>
ON DUPLICATE KEY UPDATE option_value = '1';
                    </code>
                </div>
                
                <div class="card">
                    <h2>Troubleshooting</h2>
                    <ul>
                        <li><strong>Plugin not showing as activated:</strong> Clear any caching plugins and refresh the page.</li>
                        <li><strong>Updates not working:</strong> Check your server's ability to make outbound HTTP requests.</li>
                        <li><strong>Red X still showing:</strong> The weLaunch framework may need to be installed separately.</li>
                        <li><strong>Functionality issues:</strong> Ensure WooCommerce is active and properly configured.</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Load initial status
            loadStatus();
            loadUpdateInfo();
            
            // Verify status button
            $('#verify-status').click(function() {
                loadStatus();
            });
            
            // Reactivate button
            $('#reactivate-plugin').click(function() {
                reactivatePlugin();
            });
            
            // Check updates button
            $('#check-updates').click(function() {
                loadUpdateInfo();
            });
            
            function loadStatus() {
                $('#status-display').html('<p>Loading...</p>');
                
                $.post(ajaxurl, {
                    action: 'wc_pdf_invoices_verify',
                    nonce: '<?php echo wp_create_nonce('wc_pdf_verify'); ?>'
                }, function(response) {
                    if (response.success) {
                        displayStatus(response.data);
                    } else {
                        $('#status-display').html('<p style="color: red;">Error loading status: ' + response.data + '</p>');
                    }
                });
            }
            
            function reactivatePlugin() {
                $('#status-display').html('<p>Reactivating...</p>');
                
                $.post(ajaxurl, {
                    action: 'wc_pdf_invoices_reactivate',
                    nonce: '<?php echo wp_create_nonce('wc_pdf_reactivate'); ?>'
                }, function(response) {
                    if (response.success) {
                        loadStatus();
                        alert('Plugin reactivated successfully!');
                    } else {
                        alert('Reactivation failed: ' + response.data);
                    }
                });
            }
            
            function loadUpdateInfo() {
                $('#update-display').html('<p>Checking...</p>');
                
                $.post(ajaxurl, {
                    action: 'wc_pdf_invoices_check_updates',
                    nonce: '<?php echo wp_create_nonce('wc_pdf_updates'); ?>'
                }, function(response) {
                    if (response.success) {
                        displayUpdateInfo(response.data);
                    } else {
                        $('#update-display').html('<p style="color: red;">Error checking updates: ' + response.data + '</p>');
                    }
                });
            }
            
            function displayStatus(data) {
                var html = '<table class="widefat">';
                html += '<tr><td><strong>Activation Status:</strong></td><td>' + (data.activated ? '<span style="color: green;">✓ Activated</span>' : '<span style="color: red;">✗ Not Activated</span>') + '</td></tr>';
                html += '<tr><td><strong>Activation Method:</strong></td><td>' + data.method + '</td></tr>';
                html += '<tr><td><strong>License Status:</strong></td><td>' + (data.license_status === 'valid' ? '<span style="color: green;">✓ Valid</span>' : '<span style="color: red;">✗ Invalid</span>') + '</td></tr>';
                html += '<tr><td><strong>Update Capability:</strong></td><td>' + (data.update_capability ? '<span style="color: green;">✓ Available</span>' : '<span style="color: red;">✗ Not Available</span>') + '</td></tr>';
                html += '</table>';
                
                $('#status-display').html(html);
            }
            
            function displayUpdateInfo(data) {
                var html = '<table class="widefat">';
                html += '<tr><td><strong>Current Version:</strong></td><td>' + data.current_version + '</td></tr>';
                html += '<tr><td><strong>Latest Version:</strong></td><td>' + data.latest_version + '</td></tr>';
                html += '<tr><td><strong>Update Available:</strong></td><td>' + (data.update_available ? '<span style="color: orange;">Yes</span>' : '<span style="color: green;">No</span>') + '</td></tr>';
                html += '<tr><td><strong>Server Communication:</strong></td><td>' + (data.server_reachable ? '<span style="color: green;">✓ Working</span>' : '<span style="color: red;">✗ Failed</span>') + '</td></tr>';
                html += '</table>';
                
                if (data.update_available) {
                    html += '<p><a href="' + data.update_url + '" class="button button-primary">Update Now</a></p>';
                }
                
                $('#update-display').html(html);
            }
        });
        </script>
        
        <style>
        .card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin: 20px 0;
            padding: 20px;
        }
        .card h2 {
            margin-top: 0;
        }
        code {
            font-family: Consolas, Monaco, monospace;
            font-size: 12px;
        }
        </style>
        <?php
    }
    
    /**
     * AJAX: Verify activation status
     */
    public function ajax_verify_status() {
        check_ajax_referer('wc_pdf_verify', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        if (function_exists('woocommerce_pdf_invoices_verify_status')) {
            $status = woocommerce_pdf_invoices_verify_status();
            wp_send_json_success($status);
        } else {
            wp_send_json_error('Verification function not available');
        }
    }
    
    /**
     * AJAX: Reactivate plugin
     */
    public function ajax_reactivate() {
        check_ajax_referer('wc_pdf_reactivate', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        if (function_exists('woocommerce_pdf_invoices_fallback_activate')) {
            $result = woocommerce_pdf_invoices_fallback_activate();
            if ($result) {
                wp_send_json_success('Plugin reactivated successfully');
            } else {
                wp_send_json_error('Reactivation failed');
            }
        } else {
            wp_send_json_error('Activation function not available');
        }
    }
    
    /**
     * AJAX: Check for updates
     */
    public function ajax_check_updates() {
        check_ajax_referer('wc_pdf_updates', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        $plugin_file = WP_PLUGIN_DIR . '/woocommerce-ultimate-pdf-invoices/woocommerce-ultimate-pdf-invoices.php';
        
        if (file_exists($plugin_file)) {
            $plugin_data = get_plugin_data($plugin_file);
            $current_version = $plugin_data['Version'];
            
            // Check for updates
            $response = wp_remote_get('https://api.welaunch.io/v1/version-check/woocommerce-ultimate-pdf-invoices', array(
                'timeout' => 15
            ));
            
            $server_reachable = !is_wp_error($response);
            $latest_version = $current_version;
            
            if ($server_reachable) {
                $body = wp_remote_retrieve_body($response);
                $data = json_decode($body, true);
                if (isset($data['version'])) {
                    $latest_version = $data['version'];
                }
            }
            
            $update_available = version_compare($current_version, $latest_version, '<');
            
            wp_send_json_success(array(
                'current_version' => $current_version,
                'latest_version' => $latest_version,
                'update_available' => $update_available,
                'server_reachable' => $server_reachable,
                'update_url' => admin_url('plugins.php')
            ));
        } else {
            wp_send_json_error('Plugin file not found');
        }
    }
}

// Initialize verification tools
new WooCommerce_PDF_Invoices_Verification();
